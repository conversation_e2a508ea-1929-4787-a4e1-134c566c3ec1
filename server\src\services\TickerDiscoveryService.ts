import { GlobalNewsService, GlobalNewsArticle } from './GlobalNewsService';
import { NewsImpactAnalysisService, MarketImpactAnalysis } from './NewsImpactAnalysisService';
import { logger } from '../utils/logger';
import axios from 'axios';

export interface DiscoveredTicker {
  ticker: string;
  companyName: string;
  sector: string;
  marketCap: number;
  discoveryReason: string;
  impactScore: number;
  confidence: number;
  newsArticles: GlobalNewsArticle[];
  tradingOpportunities: number;
  riskLevel: 'low' | 'medium' | 'high';
  timeframe: string;
  discoveredAt: Date;
}

export interface DiscoveryConfig {
  minImpactScore: number;
  minConfidence: number;
  maxTickersPerRun: number;
  excludedSectors: string[];
  minMarketCap: number;
  newsLookbackHours: number;
  analysisCategories: string[];
}

export interface TickerValidation {
  ticker: string;
  isValid: boolean;
  companyName?: string;
  sector?: string;
  marketCap?: number;
  exchange?: string;
  currency?: string;
}

export class TickerDiscoveryService {
  private globalNewsService: GlobalNewsService;
  private newsImpactService: NewsImpactAnalysisService;
  private discoveryConfig: DiscoveryConfig;

  constructor(
    newsApiKey: string,
    geminiApiKey: string,
    config?: Partial<DiscoveryConfig>
  ) {
    this.globalNewsService = new GlobalNewsService(newsApiKey);
    this.newsImpactService = new NewsImpactAnalysisService(geminiApiKey);
    
    this.discoveryConfig = {
      minImpactScore: 60,
      minConfidence: 70,
      maxTickersPerRun: 20,
      excludedSectors: ['Penny Stocks', 'OTC'],
      minMarketCap: *********, // $100M
      newsLookbackHours: 24,
      analysisCategories: ['general', 'business', 'technology', 'health'],
      ...config
    };
  }

  /**
   * Main discovery process - find new tickers from global news
   */
  public async discoverNewTickers(): Promise<DiscoveredTicker[]> {
    try {
      logger.info('Starting ticker discovery process', {
        lookbackHours: this.discoveryConfig.newsLookbackHours,
        categories: this.discoveryConfig.analysisCategories
      });

      // Step 1: Fetch global news
      const newsArticles = await this.globalNewsService.fetchGlobalNews(
        this.discoveryConfig.analysisCategories,
        this.discoveryConfig.newsLookbackHours,
        100
      );

      // Step 2: Filter for market-relevant news
      const relevantNews = this.globalNewsService.filterMarketRelevantNews(newsArticles, 0.6);

      // Step 3: Analyze market impact
      const analyzedArticles = await this.newsImpactService.batchAnalyzeArticles(relevantNews, 3);

      // Step 4: Extract tickers from analysis
      const extractedTickers = this.newsImpactService.extractTickers(analyzedArticles);

      // Step 5: Validate tickers
      const validatedTickers = await this.validateTickers(
        extractedTickers.map(t => t.ticker)
      );

      // Step 6: Create discovered ticker objects
      const discoveredTickers = await this.createDiscoveredTickers(
        validatedTickers,
        analyzedArticles,
        extractedTickers
      );

      // Step 7: Filter and rank
      const filteredTickers = this.filterAndRankTickers(discoveredTickers);

      logger.info('Ticker discovery completed', {
        totalNews: newsArticles.length,
        relevantNews: relevantNews.length,
        extractedTickers: extractedTickers.length,
        validTickers: validatedTickers.filter(t => t.isValid).length,
        finalTickers: filteredTickers.length
      });

      return filteredTickers.slice(0, this.discoveryConfig.maxTickersPerRun);
    } catch (error) {
      logger.error('Error in ticker discovery:', error);
      throw error;
    }
  }

  /**
   * Validate ticker symbols using financial API
   */
  private async validateTickers(tickers: string[]): Promise<TickerValidation[]> {
    const validations: TickerValidation[] = [];
    const uniqueTickers = [...new Set(tickers)];

    for (const ticker of uniqueTickers) {
      try {
        // Use Alpha Vantage or similar API to validate ticker
        // For now, using a simple validation approach
        const validation = await this.validateSingleTicker(ticker);
        validations.push(validation);
        
        // Add delay to respect rate limits
        await new Promise(resolve => setTimeout(resolve, 200));
      } catch (error) {
        logger.warn(`Failed to validate ticker ${ticker}:`, error);
        validations.push({
          ticker,
          isValid: false
        });
      }
    }

    return validations;
  }

  /**
   * Validate a single ticker
   */
  private async validateSingleTicker(ticker: string): Promise<TickerValidation> {
    try {
      // Simple validation - check if ticker format is valid
      if (!/^[A-Z]{1,5}$/.test(ticker)) {
        return { ticker, isValid: false };
      }

      // In a real implementation, you would call a financial API here
      // For now, we'll use a mock validation with common tickers
      const knownTickers = new Set([
        'AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'META', 'NVDA', 'NFLX',
        'AMD', 'INTC', 'CRM', 'ORCL', 'ADBE', 'PYPL', 'UBER', 'LYFT',
        'JPM', 'BAC', 'WFC', 'GS', 'MS', 'C', 'USB', 'PNC',
        'JNJ', 'PFE', 'UNH', 'ABBV', 'MRK', 'TMO', 'DHR', 'ABT',
        'XOM', 'CVX', 'COP', 'EOG', 'SLB', 'HAL', 'OXY', 'MPC',
        'BA', 'LMT', 'RTX', 'NOC', 'GD', 'HON', 'MMM', 'CAT',
        'WMT', 'HD', 'PG', 'KO', 'PEP', 'MCD', 'NKE', 'SBUX'
      ]);

      if (knownTickers.has(ticker)) {
        return {
          ticker,
          isValid: true,
          companyName: this.getCompanyName(ticker),
          sector: this.getSector(ticker),
          marketCap: this.getMarketCap(ticker),
          exchange: 'NASDAQ',
          currency: 'USD'
        };
      }

      return { ticker, isValid: false };
    } catch (error) {
      logger.error(`Error validating ticker ${ticker}:`, error);
      return { ticker, isValid: false };
    }
  }

  /**
   * Create discovered ticker objects
   */
  private async createDiscoveredTickers(
    validatedTickers: TickerValidation[],
    analyzedArticles: (GlobalNewsArticle & { analysis: MarketImpactAnalysis })[],
    extractedTickers: { ticker: string; impactScore: number; confidence: number; opportunities: number }[]
  ): Promise<DiscoveredTicker[]> {
    const discoveredTickers: DiscoveredTicker[] = [];

    for (const validation of validatedTickers) {
      if (!validation.isValid) continue;

      const extractedData = extractedTickers.find(t => t.ticker === validation.ticker);
      if (!extractedData) continue;

      // Find related news articles
      const relatedArticles = analyzedArticles.filter(article =>
        article.analysis.affectedCompanies.some(company => company.ticker === validation.ticker) ||
        article.analysis.tradingOpportunities.some(opp => opp.tickers.includes(validation.ticker))
      );

      // Determine discovery reason
      const discoveryReason = this.generateDiscoveryReason(relatedArticles, validation.ticker);

      // Assess risk level
      const riskLevel = this.assessRiskLevel(extractedData.impactScore, extractedData.confidence);

      // Determine timeframe
      const timeframe = this.determineTimeframe(relatedArticles);

      discoveredTickers.push({
        ticker: validation.ticker,
        companyName: validation.companyName || 'Unknown',
        sector: validation.sector || 'Unknown',
        marketCap: validation.marketCap || 0,
        discoveryReason,
        impactScore: extractedData.impactScore,
        confidence: extractedData.confidence,
        newsArticles: relatedArticles.map(a => ({
          title: a.title,
          content: a.content,
          source: a.source,
          publishedAt: a.publishedAt,
          url: a.url,
          category: a.category,
          country: a.country,
          language: a.language,
          relevanceScore: a.relevanceScore
        })),
        tradingOpportunities: extractedData.opportunities,
        riskLevel,
        timeframe,
        discoveredAt: new Date()
      });
    }

    return discoveredTickers;
  }

  /**
   * Filter and rank discovered tickers
   */
  private filterAndRankTickers(tickers: DiscoveredTicker[]): DiscoveredTicker[] {
    return tickers
      .filter(ticker => 
        ticker.impactScore >= this.discoveryConfig.minImpactScore &&
        ticker.confidence >= this.discoveryConfig.minConfidence &&
        ticker.marketCap >= this.discoveryConfig.minMarketCap &&
        !this.discoveryConfig.excludedSectors.includes(ticker.sector)
      )
      .sort((a, b) => {
        // Sort by weighted score: impact * confidence * opportunities
        const scoreA = Math.abs(a.impactScore) * (a.confidence / 100) * (a.tradingOpportunities + 1);
        const scoreB = Math.abs(b.impactScore) * (b.confidence / 100) * (b.tradingOpportunities + 1);
        return scoreB - scoreA;
      });
  }

  /**
   * Generate discovery reason
   */
  private generateDiscoveryReason(
    articles: (GlobalNewsArticle & { analysis: MarketImpactAnalysis })[],
    ticker: string
  ): string {
    if (articles.length === 0) return 'Unknown discovery reason';

    const reasons = articles.map(article => {
      const companyImpact = article.analysis.affectedCompanies.find(c => c.ticker === ticker);
      if (companyImpact) {
        return `${article.title.substring(0, 100)}... (Impact: ${companyImpact.impactScore})`;
      }

      const opportunity = article.analysis.tradingOpportunities.find(opp => opp.tickers.includes(ticker));
      if (opportunity) {
        return `Trading opportunity: ${opportunity.type} (${article.title.substring(0, 80)}...)`;
      }

      return article.title.substring(0, 100) + '...';
    });

    return reasons[0]; // Return the first (most relevant) reason
  }

  /**
   * Assess risk level
   */
  private assessRiskLevel(impactScore: number, confidence: number): 'low' | 'medium' | 'high' {
    const riskScore = Math.abs(impactScore) * (1 - confidence / 100);
    
    if (riskScore < 20) return 'low';
    if (riskScore < 50) return 'medium';
    return 'high';
  }

  /**
   * Determine timeframe
   */
  private determineTimeframe(articles: (GlobalNewsArticle & { analysis: MarketImpactAnalysis })[]): string {
    if (articles.length === 0) return 'short_term';

    const timeframes = articles.map(a => a.analysis.timeframe);
    const timeframeCounts = timeframes.reduce((acc, tf) => {
      acc[tf] = (acc[tf] || 0) + 1;
      return acc;
    }, {} as { [key: string]: number });

    return Object.keys(timeframeCounts).reduce((a, b) => 
      timeframeCounts[a] > timeframeCounts[b] ? a : b
    );
  }

  // Mock data methods (replace with real API calls in production)
  private getCompanyName(ticker: string): string {
    const names: { [key: string]: string } = {
      'AAPL': 'Apple Inc.',
      'MSFT': 'Microsoft Corporation',
      'GOOGL': 'Alphabet Inc.',
      'AMZN': 'Amazon.com Inc.',
      'TSLA': 'Tesla Inc.',
      'META': 'Meta Platforms Inc.',
      'NVDA': 'NVIDIA Corporation',
      'NFLX': 'Netflix Inc.',
      'JPM': 'JPMorgan Chase & Co.',
      'JNJ': 'Johnson & Johnson',
      'XOM': 'Exxon Mobil Corporation',
      'BA': 'The Boeing Company',
      'LMT': 'Lockheed Martin Corporation',
      'RTX': 'Raytheon Technologies Corporation',
      'NOC': 'Northrop Grumman Corporation'
    };
    return names[ticker] || `${ticker} Corporation`;
  }

  private getSector(ticker: string): string {
    const sectors: { [key: string]: string } = {
      'AAPL': 'Technology',
      'MSFT': 'Technology',
      'GOOGL': 'Technology',
      'AMZN': 'Consumer Discretionary',
      'TSLA': 'Consumer Discretionary',
      'META': 'Technology',
      'NVDA': 'Technology',
      'NFLX': 'Communication Services',
      'JPM': 'Financial Services',
      'JNJ': 'Healthcare',
      'XOM': 'Energy',
      'BA': 'Industrials',
      'LMT': 'Industrials',
      'RTX': 'Industrials',
      'NOC': 'Industrials'
    };
    return sectors[ticker] || 'Unknown';
  }

  private getMarketCap(ticker: string): number {
    const marketCaps: { [key: string]: number } = {
      'AAPL': 3000000000000,
      'MSFT': 2800000000000,
      'GOOGL': *********0000,
      'AMZN': 1500000000000,
      'TSLA': 800000000000,
      'META': 750000000000,
      'NVDA': 1800000000000,
      'NFLX': 200000000000,
      'JPM': 450000000000,
      'JNJ': 400000000000,
      'XOM': 350000000000,
      'BA': 120000000000,
      'LMT': 1**********0,
      'RTX': 140000000000,
      'NOC': 70000000000
    };
    return marketCaps[ticker] || **********;
  }

  /**
   * Get discovery statistics
   */
  public async getDiscoveryStats(): Promise<{
    totalNewsAnalyzed: number;
    tickersDiscovered: number;
    averageConfidence: number;
    topSectors: string[];
    riskDistribution: { [key: string]: number };
  }> {
    // This would typically query a database of discovery history
    // For now, returning mock stats
    return {
      totalNewsAnalyzed: 150,
      tickersDiscovered: 12,
      averageConfidence: 78,
      topSectors: ['Technology', 'Healthcare', 'Energy', 'Industrials'],
      riskDistribution: { low: 4, medium: 6, high: 2 }
    };
  }
}
