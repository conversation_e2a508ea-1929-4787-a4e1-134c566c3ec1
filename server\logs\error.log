{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:48:35)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:24:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:95:31)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:53:37)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:36:17)","timestamp":"2025-06-22T12:30:19.621Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:48:35)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:24:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:95:31)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:53:37)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:36:17)","timestamp":"2025-06-22T12:30:25.537Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:48:35)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:24:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:95:31)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:53:37)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:36:17)","timestamp":"2025-06-22T12:30:31.581Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:48:35)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:24:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:95:31)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:53:37)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:36:17)","timestamp":"2025-06-22T12:30:37.741Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:48:35)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:24:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:95:31)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:53:37)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:36:17)","timestamp":"2025-06-22T12:30:43.855Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:48:35)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:24:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:95:31)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:53:37)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:36:17)","timestamp":"2025-06-22T12:30:49.850Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:48:35)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:24:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:95:31)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:53:37)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:36:17)","timestamp":"2025-06-22T12:30:55.731Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:48:35)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:24:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:95:31)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:53:37)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:36:17)","timestamp":"2025-06-22T12:31:12.621Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:48:35)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:24:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:95:31)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:53:37)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:36:17)","timestamp":"2025-06-22T12:31:44.343Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:48:35)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:24:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:95:31)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:53:37)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:36:17)","timestamp":"2025-06-22T12:32:10.917Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:48:35)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:24:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:95:31)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:53:37)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:36:17)","timestamp":"2025-06-22T12:32:17.167Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:48:35)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:24:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:95:31)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:53:37)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:36:17)","timestamp":"2025-06-22T12:32:23.050Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:48:35)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:24:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:95:31)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:53:37)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:36:17)","timestamp":"2025-06-22T12:32:29.301Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:48:35)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:24:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:95:31)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:53:37)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:36:17)","timestamp":"2025-06-22T12:32:35.312Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:48:35)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:24:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:95:31)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:53:37)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:36:17)","timestamp":"2025-06-22T12:33:17.143Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:48:35)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:24:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:95:31)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:53:37)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:36:17)","timestamp":"2025-06-22T12:33:37.398Z"}
{"level":"error","message":"Port 3001 is already in use.","service":"trading-bot","timestamp":"2025-06-22T12:37:37.744Z"}
{"level":"error","message":"Port 3001 is already in use.","service":"trading-bot","timestamp":"2025-06-22T12:40:37.174Z"}
{"level":"error","message":"Port 3001 is already in use.","service":"trading-bot","timestamp":"2025-06-22T12:41:01.971Z"}
{"level":"error","message":"Port 3001 is already in use.","service":"trading-bot","timestamp":"2025-06-22T12:49:51.793Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)","timestamp":"2025-06-22T13:00:10.019Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)","timestamp":"2025-06-22T13:00:15.950Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)","timestamp":"2025-06-22T13:00:22.019Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)","timestamp":"2025-06-22T13:00:28.078Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)","timestamp":"2025-06-22T13:00:46.771Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)","timestamp":"2025-06-22T13:00:53.994Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)","timestamp":"2025-06-22T13:01:11.339Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)","timestamp":"2025-06-22T13:01:17.305Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)","timestamp":"2025-06-22T13:01:23.426Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)","timestamp":"2025-06-22T13:01:29.290Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)","timestamp":"2025-06-22T13:01:35.227Z"}
{"level":"error","message":"Error parsing sentiment response: Unexpected non-whitespace character after JSON at position 3263 (line 32 column 1)","service":"trading-bot","stack":"SyntaxError: Unexpected non-whitespace character after JSON at position 3263 (line 32 column 1)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)","timestamp":"2025-06-22T13:01:51.306Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)","timestamp":"2025-06-22T13:02:57.846Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)","timestamp":"2025-06-22T13:03:18.182Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)","timestamp":"2025-06-22T13:03:39.145Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)","timestamp":"2025-06-22T13:03:56.729Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)","timestamp":"2025-06-22T13:04:06.397Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)","timestamp":"2025-06-22T13:04:12.693Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)","timestamp":"2025-06-22T13:04:18.950Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)","timestamp":"2025-06-22T13:04:44.641Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)","timestamp":"2025-06-22T13:04:50.406Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)","timestamp":"2025-06-22T13:04:56.346Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)","timestamp":"2025-06-22T13:05:23.160Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)","timestamp":"2025-06-22T13:06:21.998Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)","timestamp":"2025-06-22T13:06:29.245Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)","timestamp":"2025-06-22T13:07:13.494Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)","timestamp":"2025-06-22T13:07:19.232Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)","timestamp":"2025-06-22T13:07:25.262Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)","timestamp":"2025-06-22T13:07:31.187Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)","timestamp":"2025-06-22T13:07:37.219Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)","timestamp":"2025-06-22T13:07:43.062Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)","timestamp":"2025-06-22T13:07:49.128Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)","timestamp":"2025-06-22T13:07:55.707Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)","timestamp":"2025-06-22T13:08:01.729Z"}
[2025-06-22 16:15:10.209] ERROR: Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2) | {"stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)"}
[2025-06-22 16:15:16.468] ERROR: Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2) | {"stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)"}
[2025-06-22 16:15:22.696] ERROR: Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2) | {"stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)"}
[2025-06-22 16:15:28.902] ERROR: Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2) | {"stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)"}
[2025-06-22 16:15:35.079] ERROR: Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2) | {"stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)"}
[2025-06-22 16:15:51.322] ERROR: Error fetching historical data for INVALID: No data found, symbol may be delisted | {"stack":"Error: No data found, symbol may be delisted\n    at Object.<anonymous> (C:\\work\\Trading Bot\\server\\node_modules\\yahoo-finance2\\dist\\cjs\\src\\lib\\yahooFinanceFetch.js:108:27)\n    at Generator.next (<anonymous>)\n    at fulfilled (C:\\work\\Trading Bot\\server\\node_modules\\yahoo-finance2\\dist\\cjs\\src\\lib\\yahooFinanceFetch.js:5:58)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)"}
[2025-06-22 16:15:52.437] ERROR: Error fetching historical data for INVALID: No data found, symbol may be delisted | {"stack":"Error: No data found, symbol may be delisted\n    at Object.<anonymous> (C:\\work\\Trading Bot\\server\\node_modules\\yahoo-finance2\\dist\\cjs\\src\\lib\\yahooFinanceFetch.js:108:27)\n    at Generator.next (<anonymous>)\n    at fulfilled (C:\\work\\Trading Bot\\server\\node_modules\\yahoo-finance2\\dist\\cjs\\src\\lib\\yahooFinanceFetch.js:5:58)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)"}
[2025-06-22 16:15:52.770] ERROR: Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2) | {"stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)"}
[2025-06-22 16:15:58.738] ERROR: Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2) | {"stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)"}
[2025-06-22 16:16:06.312] ERROR: Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2) | {"stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)"}
[2025-06-22 16:16:38.195] ERROR: Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2) | {"stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)"}
[2025-06-22 16:16:44.236] ERROR: Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2) | {"stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)"}
[2025-06-22 16:16:50.189] ERROR: Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2) | {"stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)"}
[2025-06-22 16:16:56.426] ERROR: Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2) | {"stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)"}
[2025-06-22 16:17:02.631] ERROR: Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2) | {"stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)"}
[2025-06-22 16:18:30.313] ERROR: Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2) | {"stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)"}
[2025-06-22 16:18:48.570] ERROR: Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2) | {"stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)"}
[2025-06-22 16:19:20.382] ERROR: Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2) | {"stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)"}
[2025-06-22 16:19:39.678] ERROR: Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2) | {"stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)"}
[2025-06-22 16:19:45.475] ERROR: Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2) | {"stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)"}
[2025-06-22 16:20:00.207] ERROR: Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2) | {"stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)"}
[2025-06-22 16:20:05.935] ERROR: Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2) | {"stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)"}
[2025-06-22 16:20:31.768] ERROR: Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2) | {"stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)"}
[2025-06-22 16:20:37.743] ERROR: Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2) | {"stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)"}
[2025-06-22 16:20:43.854] ERROR: Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2) | {"stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)"}
[2025-06-22 16:21:31.040] ERROR: Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2) | {"stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)"}
[2025-06-22 16:22:11.949] ERROR: Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2) | {"stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)"}
[2025-06-22 16:22:18.963] ERROR: Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2) | {"stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)"}
[2025-06-22 16:22:45.855] ERROR: Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2) | {"stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)"}
[2025-06-22 16:22:51.284] ERROR: Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2) | {"stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)"}
[2025-06-22 16:22:57.238] ERROR: Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2) | {"stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)"}
[2025-06-22 16:23:02.965] ERROR: Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2) | {"stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)"}
[2025-06-22 16:23:09.160] ERROR: Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2) | {"stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)"}
[2025-06-22 16:23:21.934] ERROR: Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2) | {"stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)"}
[2025-06-22 16:23:28.101] ERROR: Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2) | {"stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)"}
[2025-06-22 16:23:34.860] ERROR: Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2) | {"stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)"}
[2025-06-22 16:23:48.342] ERROR: Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2) | {"stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)"}
[2025-06-22 16:24:12.470] ERROR: Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2) | {"stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)"}
[2025-06-22 16:25:01.942] ERROR: Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2) | {"stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)"}
[2025-06-22 16:30:20.530] ERROR: Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2) | {"stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)"}
[2025-06-22 16:30:26.455] ERROR: Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2) | {"stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)"}
[2025-06-22 16:30:32.597] ERROR: Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2) | {"stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)"}
[2025-06-22 16:30:38.525] ERROR: Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2) | {"stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)"}
[2025-06-22 16:30:53.077] ERROR: Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2) | {"stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)"}
[2025-06-22 16:31:00.852] ERROR: Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2) | {"stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)"}
[2025-06-22 16:31:18.417] ERROR: Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2) | {"stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)"}
