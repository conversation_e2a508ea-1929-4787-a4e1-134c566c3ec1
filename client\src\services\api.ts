import axios from 'axios';
import { ApiResponse, TickerData, DashboardData, LogEntry, OHLCV } from '../types';
import { logger, logApiCall, logError, measurePerformance } from '../utils/logger';

const API_BASE_URL = '/api';

const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
});

// Add request interceptor for logging
api.interceptors.request.use(
  (config) => {
    const startTime = Date.now();
    (config as any).metadata = { startTime };

    logger.info('API', `Starting ${config.method?.toUpperCase()} ${config.url}`, 'request', {
      url: config.url,
      method: config.method,
      params: config.params,
      timeout: config.timeout
    });

    return config;
  },
  (error) => {
    logError('API', 'request-setup', error);
    return Promise.reject(error);
  }
);

// Add response interceptor for logging and error handling
api.interceptors.response.use(
  (response) => {
    const duration = Date.now() - ((response.config as any).metadata?.startTime || 0);
    const method = response.config.method?.toUpperCase() || 'GET';
    const url = response.config.url || '';

    logApiCall(method, url, duration, response.status, {
      dataSize: JSON.stringify(response.data).length,
      success: response.data?.success
    });

    return response;
  },
  (error) => {
    const duration = Date.now() - ((error.config as any)?.metadata?.startTime || 0);
    const method = error.config?.method?.toUpperCase() || 'GET';
    const url = error.config?.url || '';
    const status = error.response?.status || 0;

    logApiCall(method, url, duration, status, {
      error: error.message,
      errorCode: error.code,
      responseData: error.response?.data
    });

    logError('API', `${method} ${url}`, error, {
      status,
      duration,
      responseData: error.response?.data
    });

    return Promise.reject(error);
  }
);

export const tickerApi = {
  // Get all tickers
  getAll: async (): Promise<TickerData[]> => {
    const response = await api.get<ApiResponse<TickerData[]>>('/tickers');
    return response.data.data || [];
  },

  // Get specific ticker
  getByTicker: async (ticker: string): Promise<TickerData> => {
    const response = await api.get<ApiResponse<TickerData>>(`/tickers/${ticker}`);
    if (!response.data.success || !response.data.data) {
      throw new Error(response.data.error || 'Failed to fetch ticker data');
    }
    return response.data.data;
  },

  // Get historical data for a ticker
  getHistory: async (ticker: string): Promise<OHLCV[]> => {
    const response = await api.get<ApiResponse<OHLCV[]>>(`/tickers/${ticker}/history`);
    const historyData = response.data.data || [];
    
    // Convert timestamp strings to Date objects
    return historyData.map(item => ({
      ...item,
      timestamp: new Date(item.timestamp),
    }));
  },

  // Get dashboard summary
  getDashboardSummary: async (): Promise<DashboardData> => {
    const response = await api.get<ApiResponse<DashboardData>>('/tickers/dashboard/summary');
    if (!response.data.success || !response.data.data) {
      throw new Error(response.data.error || 'Failed to fetch dashboard data');
    }
    return response.data.data;
  },



  // Get logs
  getLogs: async (params?: { limit?: number; type?: string; ticker?: string }): Promise<LogEntry[]> => {
    const response = await api.get<ApiResponse<LogEntry[]>>('/tickers/logs', { params });
    return response.data.data || [];
  },
};

export const healthApi = {
  // Health check
  check: async () => {
    const response = await api.get('/health');
    return response.data;
  },
}; 